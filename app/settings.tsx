import React, { useState, useEffect } from 'react';

import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableOpacity,
    ImageBackground,
    Animated,
    StatusBar,
    Platform
} from 'react-native';

import { Link, useFocusEffect, router } from 'expo-router';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';
import { ArrowRight04Icon } from '@hugeicons/core-free-icons';



import Toast from 'react-native-toast-message';

import { Storage } from 'expo-sqlite/kv-store';

import uuid from 'react-native-uuid';

import env from '../env';



export default function SettingsScreen() {
    const insets = useSafeAreaInsets()

    const [isDev, setIsDev] = useState(false)


    const [isLoggedIn, setIsLoggedIn] = useState(false)
    // const [uuidstr, setUuidstr] = useState('')
    const [userToken, setUserToken] = useState('')



    useEffect(() => {
        console.log('a')

        // let a = uuid.v4()
        // setUuidstr(a)
    }, [])

    // 页面重新获得焦点时
    useFocusEffect(
        React.useCallback(() => {
            console.log('b')

            checkIsDev()

            checkLoginStatus()

        }, [])
    )

    let checkIsDev = async () => {
        const isDevValue = await Storage.getItem('isDev')
        console.log('isDev', isDevValue, typeof isDevValue)

        // 正确处理字符串值
        if (isDevValue === 'true') {
            setIsDev(true)
        } else {
            setIsDev(false)
        }
    }

    const checkLoginStatus = async () => {
        const token = await Storage.getItem('userToken')
        console.log(token)

        if (token) {
            setIsLoggedIn(true)
            setUserToken(token)
        } else {
            setIsLoggedIn(false)
        }
    };

    const handleFastLogin = async () => {
        console.log("func handleFastLogin")

        try {
            const response = await fetch(env.BASE_URL + '/x_mooood_user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    do_sth: 'denglu',
                    name: '1',
                    password: '1',
                }),
            });

            const data = await response.json();

            if (response.ok) {
                console.log('登录返回数据', data);
                // console.log(JSON.stringify(data));

                if (data?.code === 6666) {
                    console.log('登录成功！！！')

                    try {
                        // 从 data 字段中获取 token
                        const responseData = data?.data;
                        const token = responseData?.token;

                        if (token) {
                            await Storage.setItem('userToken', token);
                            console.log('Token 已保存到本地:', token);

                            Toast.show({
                                type: 'success',
                                text1: '登录成功',
                            });

                            // 登录成功后跳转到主页
                            router.replace('/');
                        } else {
                            console.log('未找到 token 字段')
                        }
                    } catch (error) {
                        console.error('fail to save token', error);
                    }
                } else if (data?.code === 4001) {
                    console.log(data?.msg)
                    Toast.show({
                        type: 'error',
                        text1: data?.msg,
                    });
                } else if (data?.code === 4002) {
                    console.log(data?.msg)
                    Toast.show({
                        type: 'error',
                        text1: data?.msg,
                    });
                } else if (data?.code === 4003) {
                    console.log(data?.msg)
                    Toast.show({
                        type: 'error',
                        text1: data?.msg,
                    });
                } else if (data?.code === 4004) {
                    console.log(data?.msg)
                    Toast.show({
                        type: 'error',
                        text1: data?.msg,
                    });
                } else {
                    console.log('注册失败:', data);
                }
            } else {
                console.log('注册失败了了了', data);
            }
        } catch (error) {
            console.error('网络错误:', error);
        }
    }

    const tap_version_view = async () => {
        await Storage.setItem('isDev', 'true')
        setIsDev(true)
    }

    const handleTopLeftBack = () => {
        router.back()
    }



    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}> 设置 </Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <View style={styles.content}>
                        {/* <View>
                            <Text> uuidstr </Text>
                            <Text> {uuidstr} </Text>
                        </View> */}





                        {/* 如果未登录，显示注册和登录按钮 */}
                        {/* {!isLoggedIn && ( */}
                        <>
                            <TouchableOpacity onPress={() => router.push('/login-reg')}>
                                <View style={styles.login_reg_hang}>
                                    <Text style={styles.login_reg_hang_text}>注册</Text>
                                </View>
                            </TouchableOpacity>

                            <TouchableOpacity onPress={() => router.push('/login-in')}>
                                <View style={styles.login_in_hang}>
                                    <Text style={styles.login_in_hang_text}>登录</Text>
                                </View>
                            </TouchableOpacity>

                            <TouchableOpacity onPress={() => handleFastLogin()}>
                                <View style={styles.login_in_hang}>
                                    <Text style={styles.login_in_hang_text}>快速登录 1 1</Text>
                                </View>
                            </TouchableOpacity>


                            <TouchableOpacity onPress={() => tap_version_view()}>
                                <View style={styles.login_in_hang}>
                                    <Text style={styles.login_in_hang_text}>Version 0.5.0</Text>
                                </View>
                            </TouchableOpacity>
                        </>
                        {/* )} */}





                        {
                            isDev &&
                            <View style={styles.dev_mode}>
                                <View style={styles.dev_list}>
                                    <View style={styles.dev_list_item}>
                                        <TouchableOpacity onPress={() => router.push('/z-dev-user-token')} style={styles.dev_list_item_link}>
                                            <Text>userToken</Text>

                                            <View style={styles.dev_list_item_spacer}></View>

                                            <HugeiconsIcon
                                                style={styles.dev_list_item_icon}
                                                icon={ArrowRight04Icon}
                                                size={40}
                                                color="black"
                                                strokeWidth={1.2}
                                            />
                                        </TouchableOpacity>
                                    </View>

                                    <View style={styles.dev_list_item}>
                                        <Text>hang 2</Text>
                                    </View>

                                    <View style={styles.dev_list_item}>
                                        <Text>hang 3</Text>
                                    </View>

                                    <View style={[styles.dev_list_item, styles.dev_list_item_last]}>
                                        <Link href="/z-dev-expo-device">
                                            <Text>设备信息</Text>
                                        </Link>
                                    </View>
                                </View>
                            </View>
                        }
                    </View>
                </View>
            </ImageBackground>
        </>

    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,

        width: '100%',
        height: '100%',
    },

    all: {
        flex: 1,

        // justifyContent: 'center',
        // alignItems: 'center',
        // backgroundColor: '#f40',
    },

    head_nav: {
        height: 50,
        width: '100%',

        // paddingHorizontal: 5,

        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

        // backgroundColor: '#f40',
    },
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },


    content: {
        flex: 1,
        padding: 16,
        // backgroundColor: 'white',

    },

    login_in_hang: {
        width: '100%',
        height: 50,

        paddingLeft: 10,
        marginTop: 10,
        justifyContent: 'center',

        borderRadius: 10,

        backgroundColor: 'black',
    },

    login_in_hang_text: {
        color: 'white',
    },


    login_reg_hang: {
        width: '100%',
        height: 50,

        paddingLeft: 10,
        marginTop: 10,

        justifyContent: 'center',

        // borderRadius: 10,
        backgroundColor: 'black',
    },

    login_reg_hang_text: {
        color: 'white',
    },

    // 新增样式
    user_info_section: {
        width: '100%',
        padding: 16,
        marginTop: 10,
        // borderRadius: 10,
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
    },

    user_info_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 8,
    },

    user_name: {
        fontSize: 18,
        color: '#333',
        marginBottom: 4,
    },

    login_time: {
        fontSize: 12,
        color: '#666',
    },

    logout_hang: {
        width: '100%',
        height: 50,

        paddingLeft: 10,
        marginTop: 10,
        justifyContent: 'center',

        // borderRadius: 10,
        backgroundColor: '#ff4444',
    },

    logout_hang_text: {
        color: 'white',
    },

    // token状态相关样式
    token_status_container: {
        marginTop: 8,
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
    },

    token_status_label: {
        fontSize: 14,
        color: '#666',
        marginRight: 8,
    },

    token_remaining_time: {
        fontSize: 14,
        fontWeight: 'bold',
    },

    token_expired: {
        color: '#ff4444',
    },

    token_valid: {
        color: '#00aa44',
    },





























    dev_mode: {
        marginTop: 100,

        flex: 1,

        // backgroundColor: '#fff',

        // borderColor: '#000',
        // borderWidth: 1,

        // borderTopWidth: 1,
        // borderRightWidth: 1,
        // borderLeftWidth: 1,
    },

    dev_list: {
        flex: 1,

        marginTop: 100,

        backgroundColor: '#fff',

    },

    dev_list_item: {
        height: 55,
        justifyContent: 'center',

        backgroundColor: 'red',


        borderTopWidth: 1,
        borderTopColor: '#000',

        borderLeftWidth: 1,
        borderLeftColor: '#000',

        borderRightWidth: 1,
        borderRightColor: '#000',
    },

    dev_list_item_last: {
        borderBottomWidth: 1,
        borderBottomColor: '#000',
    },

    dev_list_item_link: {
        flex: 1,

        paddingLeft: 10,

        backgroundColor: 'green',

        flexDirection: 'row',
        alignItems: 'center',
        // justifyContent: 'space-between',
    },

    dev_list_item_spacer: {
        flex: 1,
        height: 20,

        backgroundColor: 'blue',

    },

    dev_list_item_icon: {
        backgroundColor: 'yellow',
    }

});
